use serde::{Deserialize, Serialize};
use crate::types::{Price, Quantity, Money, Currency};
use crate::errors::{TtQuantError, TtQuantResult};

/// Instrument type enumeration
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum InstrumentType {
    Spot,
    Future,
    Option,
    Perpetual,
    Index,
    Forex,
    CFD,
}

/// Instrument class for categorization
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum InstrumentClass {
    Crypto,
    Forex,
    Equity,
    Commodity,
    Bond,
    Index,
}

/// Core instrument definition
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Instrument {
    /// Unique instrument identifier
    pub id: String,
    /// Trading symbol
    pub symbol: String,
    /// Venue/exchange identifier
    pub venue: String,
    /// Instrument type
    pub instrument_type: InstrumentType,
    /// Instrument class
    pub instrument_class: InstrumentClass,
    /// Base currency (for crypto pairs, forex)
    pub base_currency: Currency,
    /// Quote currency
    pub quote_currency: Currency,
    /// Settlement currency (for derivatives)
    pub settlement_currency: Option<Currency>,
    /// Minimum price increment (tick size)
    pub tick_size: Price,
    /// Minimum quantity increment (lot size)
    pub lot_size: Quantity,
    /// Minimum order quantity
    pub min_quantity: Quantity,
    /// Maximum order quantity
    pub max_quantity: Option<Quantity>,
    /// Minimum order value
    pub min_value: Option<Money>,
    /// Maximum order value
    pub max_value: Option<Money>,
    /// Contract size (for derivatives)
    pub contract_size: Option<Quantity>,
    /// Multiplier for P&L calculation
    pub multiplier: f64,
    /// Whether instrument is currently tradable
    pub is_tradable: bool,
    /// Margin requirement (percentage)
    pub margin_requirement: Option<f64>,
    /// Maker fee rate
    pub maker_fee: Option<f64>,
    /// Taker fee rate
    pub taker_fee: Option<f64>,
}

impl Instrument {
    /// Create a new instrument
    pub fn new(
        symbol: String,
        venue: String,
        instrument_type: InstrumentType,
        instrument_class: InstrumentClass,
        base_currency: Currency,
        quote_currency: Currency,
        tick_size: Price,
        lot_size: Quantity,
        min_quantity: Quantity,
    ) -> Self {
        let id = format!("{}.{}", symbol, venue);
        
        Self {
            id,
            symbol,
            venue,
            instrument_type,
            instrument_class,
            base_currency,
            quote_currency,
            settlement_currency: None,
            tick_size,
            lot_size,
            min_quantity,
            max_quantity: None,
            min_value: None,
            max_value: None,
            contract_size: None,
            multiplier: 1.0,
            is_tradable: true,
            margin_requirement: None,
            maker_fee: None,
            taker_fee: None,
        }
    }

    /// Set settlement currency
    pub fn with_settlement_currency(mut self, currency: Currency) -> Self {
        self.settlement_currency = Some(currency);
        self
    }

    /// Set maximum quantity
    pub fn with_max_quantity(mut self, max_quantity: Quantity) -> Self {
        self.max_quantity = Some(max_quantity);
        self
    }

    /// Set minimum order value
    pub fn with_min_value(mut self, min_value: Money) -> Self {
        self.min_value = Some(min_value);
        self
    }

    /// Set contract size (for derivatives)
    pub fn with_contract_size(mut self, contract_size: Quantity) -> Self {
        self.contract_size = Some(contract_size);
        self
    }

    /// Set multiplier
    pub fn with_multiplier(mut self, multiplier: f64) -> Self {
        self.multiplier = multiplier;
        self
    }

    /// Set margin requirement
    pub fn with_margin_requirement(mut self, margin_requirement: f64) -> Self {
        self.margin_requirement = Some(margin_requirement);
        self
    }

    /// Set fee rates
    pub fn with_fees(mut self, maker_fee: f64, taker_fee: f64) -> Self {
        self.maker_fee = Some(maker_fee);
        self.taker_fee = Some(taker_fee);
        self
    }

    /// Set tradable status
    pub fn with_tradable(mut self, is_tradable: bool) -> Self {
        self.is_tradable = is_tradable;
        self
    }

    /// Validate a price against tick size
    pub fn validate_price(&self, price: &Price) -> TtQuantResult<()> {
        if !self.is_valid_tick(price) {
            return Err(TtQuantError::instrument_error(
                format!("Price {} does not conform to tick size {}", price, self.tick_size)
            ));
        }
        Ok(())
    }

    /// Validate a quantity against lot size and limits
    pub fn validate_quantity(&self, quantity: &Quantity) -> TtQuantResult<()> {
        if !self.is_valid_lot(quantity) {
            return Err(TtQuantError::instrument_error(
                format!("Quantity {} does not conform to lot size {}", quantity, self.lot_size)
            ));
        }

        if *quantity < self.min_quantity {
            return Err(TtQuantError::instrument_error(
                format!("Quantity {} is below minimum {}", quantity, self.min_quantity)
            ));
        }

        if let Some(max_qty) = &self.max_quantity {
            if *quantity > *max_qty {
                return Err(TtQuantError::instrument_error(
                    format!("Quantity {} exceeds maximum {}", quantity, max_qty)
                ));
            }
        }

        Ok(())
    }

    /// Check if price conforms to tick size
    pub fn is_valid_tick(&self, price: &Price) -> bool {
        if self.tick_size.is_zero() {
            return true;
        }

        // Convert both to same precision for comparison
        let max_precision = price.precision.max(self.tick_size.precision);
        let price_normalized = price.round_to_precision(max_precision).unwrap_or(*price);
        let tick_normalized = self.tick_size.round_to_precision(max_precision).unwrap_or(self.tick_size);

        let price_raw = price_normalized.raw;
        let tick_raw = tick_normalized.raw;

        if tick_raw == 0 {
            return true;
        }

        price_raw % tick_raw == 0
    }

    /// Check if quantity conforms to lot size
    pub fn is_valid_lot(&self, quantity: &Quantity) -> bool {
        let qty_raw = quantity.raw as f64;
        let lot_raw = self.lot_size.raw as f64;
        
        if lot_raw == 0.0 {
            return true;
        }

        let remainder = qty_raw % lot_raw;
        remainder.abs() < 1e-9
    }

    /// Round price to nearest valid tick
    pub fn round_price_to_tick(&self, price: &Price) -> TtQuantResult<Price> {
        if self.tick_size.is_zero() {
            return Ok(*price);
        }

        let tick_value = self.tick_size.as_f64();
        let price_value = price.as_f64();
        let rounded_value = (price_value / tick_value).round() * tick_value;
        
        Price::new(rounded_value, price.precision)
            .map_err(|e| TtQuantError::instrument_error(e.to_string()))
    }

    /// Round quantity to nearest valid lot
    pub fn round_quantity_to_lot(&self, quantity: &Quantity) -> TtQuantResult<Quantity> {
        if self.lot_size.is_zero() {
            return Ok(*quantity);
        }

        let lot_value = self.lot_size.as_f64();
        let qty_value = quantity.as_f64();
        let rounded_value = (qty_value / lot_value).round() * lot_value;
        
        Quantity::new(rounded_value, quantity.precision)
            .map_err(|e| TtQuantError::instrument_error(e.to_string()))
    }

    /// Calculate order value in quote currency
    pub fn calculate_order_value(&self, quantity: &Quantity, price: &Price) -> TtQuantResult<Money> {
        let value = quantity.as_f64() * price.as_f64() * self.multiplier;
        Money::new(value, self.quote_currency.clone())
            .map_err(|e| TtQuantError::calculation_error(e.to_string()))
    }

    /// Calculate required margin for position
    pub fn calculate_margin(&self, quantity: &Quantity, price: &Price) -> TtQuantResult<Option<Money>> {
        if let Some(margin_req) = self.margin_requirement {
            let order_value = self.calculate_order_value(quantity, price)?;
            let margin_amount = order_value.as_f64() * margin_req;
            let margin_currency = self.settlement_currency.clone().unwrap_or(self.quote_currency.clone());
            
            Ok(Some(Money::new(margin_amount, margin_currency)
                .map_err(|e| TtQuantError::calculation_error(e.to_string()))?))
        } else {
            Ok(None)
        }
    }

    /// Get trading fee for given quantity and side
    pub fn calculate_fee(&self, quantity: &Quantity, price: &Price, is_maker: bool) -> TtQuantResult<Option<Money>> {
        let fee_rate = if is_maker {
            self.maker_fee
        } else {
            self.taker_fee
        };

        if let Some(rate) = fee_rate {
            let order_value = self.calculate_order_value(quantity, price)?;
            let fee_amount = order_value.as_f64() * rate;
            
            Ok(Some(Money::new(fee_amount, self.quote_currency.clone())
                .map_err(|e| TtQuantError::calculation_error(e.to_string()))?))
        } else {
            Ok(None)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::currency::{USD, BTC};

    #[test]
    fn test_instrument_creation() {
        let instrument = Instrument::new(
            "BTCUSD".to_string(),
            "binance".to_string(),
            InstrumentType::Spot,
            InstrumentClass::Crypto,
            BTC(),
            USD(),
            Price::new(0.01, 2).unwrap(),
            Quantity::new(0.00001, 5).unwrap(),
            Quantity::new(0.001, 3).unwrap(),
        );

        assert_eq!(instrument.id, "BTCUSD.binance");
        assert_eq!(instrument.symbol, "BTCUSD");
        assert_eq!(instrument.venue, "binance");
        assert!(instrument.is_tradable);
    }

    #[test]
    fn test_price_validation() {
        let instrument = Instrument::new(
            "BTCUSD".to_string(),
            "binance".to_string(),
            InstrumentType::Spot,
            InstrumentClass::Crypto,
            BTC(),
            USD(),
            Price::new(0.01, 2).unwrap(),
            Quantity::new(0.00001, 5).unwrap(),
            Quantity::new(0.001, 3).unwrap(),
        );

        // Valid price (multiple of tick size)
        let valid_price = Price::new(50000.00, 2).unwrap();
        assert!(instrument.validate_price(&valid_price).is_ok());

        // Invalid price (not multiple of tick size)
        let invalid_price = Price::new(50000.005, 3).unwrap();
        assert!(instrument.validate_price(&invalid_price).is_err());
    }

    #[test]
    fn test_quantity_validation() {
        let instrument = Instrument::new(
            "BTCUSD".to_string(),
            "binance".to_string(),
            InstrumentType::Spot,
            InstrumentClass::Crypto,
            BTC(),
            USD(),
            Price::new(0.01, 2).unwrap(),
            Quantity::new(0.00001, 5).unwrap(),
            Quantity::new(0.001, 3).unwrap(),
        );

        // Valid quantity
        let valid_qty = Quantity::new(0.001, 3).unwrap();
        assert!(instrument.validate_quantity(&valid_qty).is_ok());

        // Too small quantity
        let small_qty = Quantity::new(0.0005, 4).unwrap();
        assert!(instrument.validate_quantity(&small_qty).is_err());
    }

    #[test]
    fn test_order_value_calculation() {
        let instrument = Instrument::new(
            "BTCUSD".to_string(),
            "binance".to_string(),
            InstrumentType::Spot,
            InstrumentClass::Crypto,
            BTC(),
            USD(),
            Price::new(0.01, 2).unwrap(),
            Quantity::new(0.00001, 5).unwrap(),
            Quantity::new(0.001, 3).unwrap(),
        );

        let quantity = Quantity::new(1.0, 8).unwrap();
        let price = Price::new(50000.0, 2).unwrap();
        
        let order_value = instrument.calculate_order_value(&quantity, &price).unwrap();
        assert_eq!(order_value.as_f64(), 50000.0);
        assert_eq!(order_value.currency, USD());
    }
}
