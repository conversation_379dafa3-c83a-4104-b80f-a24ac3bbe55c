use serde::{Deserialize, Serialize};
use std::cmp::Ordering;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtResult};
use std::ops::{Add, Sub, Mul, Div, AddAssign, SubAssign};
use std::str::FromStr;
use anyhow::{anyhow, Result};
use rust_decimal::Decimal;

use super::fixed::{
    QuantityRaw, QUANTITY_MAX, QUANTITY_MIN, FIXED_PRECISION,
    f64_to_fixed_u64, fixed_u64_to_f64, check_fixed_precision, check_in_range_f64
};

/// Represents a quantity with non-negative value and specified precision
/// 
/// Used for order quantities, position sizes, and other non-negative amounts.
/// Based on Nautilus Trader's Quantity implementation.
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Quantity {
    /// Raw fixed-point value (unsigned for non-negative quantities)
    pub raw: QuantityRaw,
    /// Number of decimal places (0-9)
    pub precision: u8,
}

impl Quantity {
    /// Create a new Quantity with validation
    pub fn new(value: f64, precision: u8) -> Result<Self> {
        check_in_range_f64(value, QUANTITY_MIN, QUANTITY_MAX, "quantity")?;
        check_fixed_precision(precision)?;
        
        if value < 0.0 {
            return Err(anyhow!("Quantity cannot be negative: {}", value));
        }
        
        let raw = f64_to_fixed_u64(value, precision)?;
        Ok(Quantity { raw, precision })
    }

    /// Create Quantity from raw fixed-point value
    pub fn from_raw(raw: QuantityRaw, precision: u8) -> Result<Self> {
        check_fixed_precision(precision)?;
        Ok(Quantity { raw, precision })
    }

    /// Create Quantity from string
    pub fn from_str_with_precision(s: &str, precision: u8) -> Result<Self> {
        let value: f64 = s.parse()
            .map_err(|_| anyhow!("Invalid quantity string: {}", s))?;
        Self::new(value, precision)
    }

    /// Convert to f64
    pub fn as_f64(&self) -> f64 {
        fixed_u64_to_f64(self.raw, self.precision)
    }

    /// Convert to Decimal for precise calculations
    pub fn as_decimal(&self) -> Decimal {
        let value = self.as_f64();
        Decimal::from_f64_retain(value).unwrap_or_default()
    }

    /// Check if quantity is zero
    pub fn is_zero(&self) -> bool {
        self.raw == 0
    }

    /// Check if quantity is positive (non-zero)
    pub fn is_positive(&self) -> bool {
        self.raw > 0
    }

    /// Round to specified precision
    pub fn round_to_precision(&self, precision: u8) -> Result<Self> {
        if precision > FIXED_PRECISION {
            return Err(anyhow!("Precision {} exceeds maximum {}", precision, FIXED_PRECISION));
        }

        if precision == self.precision {
            return Ok(*self);
        }

        let value = self.as_f64();
        Self::new(value, precision)
    }

    /// Format as string with proper precision
    pub fn to_formatted_string(&self) -> String {
        format!("{:.precision$}", self.as_f64(), precision = self.precision as usize)
    }

    /// Calculate percentage of another quantity
    pub fn percentage_of(&self, other: &Quantity) -> Result<f64> {
        if other.is_zero() {
            return Err(anyhow!("Cannot calculate percentage of zero quantity"));
        }
        Ok((self.as_f64() / other.as_f64()) * 100.0)
    }
}

impl Display for Quantity {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{:.precision$}", self.as_f64(), precision = self.precision as usize)
    }
}

impl FromStr for Quantity {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self> {
        let value: f64 = s.parse()
            .map_err(|_| anyhow!("Invalid quantity string: {}", s))?;
        
        // Infer precision from decimal places
        let precision = if let Some(dot_pos) = s.find('.') {
            (s.len() - dot_pos - 1).min(FIXED_PRECISION as usize) as u8
        } else {
            0
        };
        
        Self::new(value, precision)
    }
}

impl PartialOrd for Quantity {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for Quantity {
    fn cmp(&self, other: &Self) -> Ordering {
        // Convert both to same precision for comparison
        let max_precision = self.precision.max(other.precision);
        
        let self_normalized = self.round_to_precision(max_precision).unwrap_or(*self);
        let other_normalized = other.round_to_precision(max_precision).unwrap_or(*other);
        
        self_normalized.raw.cmp(&other_normalized.raw)
    }
}

impl Add for Quantity {
    type Output = Result<Quantity>;

    fn add(self, other: Quantity) -> Self::Output {
        let max_precision = self.precision.max(other.precision);
        let self_norm = self.round_to_precision(max_precision)?;
        let other_norm = other.round_to_precision(max_precision)?;
        
        let result_raw = self_norm.raw.checked_add(other_norm.raw)
            .ok_or_else(|| anyhow!("Quantity addition overflow"))?;
        
        Quantity::from_raw(result_raw, max_precision)
    }
}

impl Sub for Quantity {
    type Output = Result<Quantity>;

    fn sub(self, other: Quantity) -> Self::Output {
        let max_precision = self.precision.max(other.precision);
        let self_norm = self.round_to_precision(max_precision)?;
        let other_norm = other.round_to_precision(max_precision)?;
        
        let result_raw = self_norm.raw.checked_sub(other_norm.raw)
            .ok_or_else(|| anyhow!("Quantity subtraction would result in negative value"))?;
        
        Quantity::from_raw(result_raw, max_precision)
    }
}

impl Mul<f64> for Quantity {
    type Output = Result<Quantity>;

    fn mul(self, scalar: f64) -> Self::Output {
        if scalar < 0.0 {
            return Err(anyhow!("Cannot multiply quantity by negative scalar"));
        }
        let new_value = self.as_f64() * scalar;
        Quantity::new(new_value, self.precision)
    }
}

impl Div<f64> for Quantity {
    type Output = Result<Quantity>;

    fn div(self, scalar: f64) -> Self::Output {
        if scalar == 0.0 {
            return Err(anyhow!("Division by zero"));
        }
        if scalar < 0.0 {
            return Err(anyhow!("Cannot divide quantity by negative scalar"));
        }
        let new_value = self.as_f64() / scalar;
        Quantity::new(new_value, self.precision)
    }
}

// Convenience constructors
impl Quantity {
    pub fn zero(precision: u8) -> Result<Self> {
        Self::new(0.0, precision)
    }

    pub fn one(precision: u8) -> Result<Self> {
        Self::new(1.0, precision)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_quantity_creation() {
        let qty = Quantity::new(123.456, 3).unwrap();
        assert_eq!(qty.precision, 3);
        assert!((qty.as_f64() - 123.456).abs() < 1e-9);
    }

    #[test]
    fn test_quantity_negative_rejected() {
        assert!(Quantity::new(-1.0, 2).is_err());
    }

    #[test]
    fn test_quantity_from_string() {
        let qty = Quantity::from_str("123.45").unwrap();
        assert_eq!(qty.precision, 2);
        assert!((qty.as_f64() - 123.45).abs() < 1e-9);
    }

    #[test]
    fn test_quantity_arithmetic() {
        let q1 = Quantity::new(100.0, 2).unwrap();
        let q2 = Quantity::new(50.0, 2).unwrap();
        
        let sum = (q1 + q2).unwrap();
        assert!((sum.as_f64() - 150.0).abs() < 1e-9);
        
        let diff = (q1 - q2).unwrap();
        assert!((diff.as_f64() - 50.0).abs() < 1e-9);
        
        // Test underflow protection
        assert!((q2 - q1).is_err());
    }

    #[test]
    fn test_quantity_comparison() {
        let q1 = Quantity::new(100.0, 2).unwrap();
        let q2 = Quantity::new(50.0, 2).unwrap();
        
        assert!(q1 > q2);
        assert!(q2 < q1);
        assert_eq!(q1, q1);
    }

    #[test]
    fn test_quantity_properties() {
        let positive = Quantity::new(100.0, 2).unwrap();
        let zero = Quantity::zero(2).unwrap();
        
        assert!(positive.is_positive());
        assert!(!positive.is_zero());
        
        assert!(zero.is_zero());
        assert!(!zero.is_positive());
    }

    #[test]
    fn test_percentage_calculation() {
        let q1 = Quantity::new(25.0, 2).unwrap();
        let q2 = Quantity::new(100.0, 2).unwrap();
        
        let percentage = q1.percentage_of(&q2).unwrap();
        assert!((percentage - 25.0).abs() < 1e-9);
    }
}
