use serde::{Deserialize, Serialize};
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtR<PERSON><PERSON>};
use std::str::FromStr;
use anyhow::{anyhow, Result};

/// Represents a currency with ISO 4217 code and precision
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Currency {
    /// ISO 4217 currency code (e.g., "USD", "EUR", "BTC")
    pub code: String,
    /// Number of decimal places for this currency
    pub precision: u8,
    /// ISO numeric code (optional, 0 if not applicable)
    pub iso_code: u16,
    /// Currency name
    pub name: String,
}

impl Currency {
    /// Create a new Currency instance
    pub fn new(code: &str, precision: u8, iso_code: u16, name: &str) -> Result<Self> {
        if code.len() != 3 {
            return Err(anyhow!("Currency code must be exactly 3 characters"));
        }

        Ok(Currency {
            code: code.to_string(),
            precision,
            iso_code,
            name: name.to_string(),
        })
    }

    /// Get currency code as string
    pub fn code_str(&self) -> &str {
        &self.code
    }

    /// Get currency name as string
    pub fn name_str(&self) -> &str {
        &self.name
    }

    /// Check if this is a cryptocurrency
    pub fn is_crypto(&self) -> bool {
        matches!(self.code_str(), "BTC" | "ETH" | "USDT" | "USDC" | "BNB" | "ADA" | "DOT" | "SOL")
    }

    /// Check if this is a fiat currency
    pub fn is_fiat(&self) -> bool {
        !self.is_crypto() && self.iso_code > 0
    }
}

impl Display for Currency {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.code_str())
    }
}

impl FromStr for Currency {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self> {
        match s {
            "USD" => Ok(USD()),
            "EUR" => Ok(EUR()),
            "GBP" => Ok(GBP()),
            "JPY" => Ok(JPY()),
            "CNY" => Ok(CNY()),
            "BTC" => Ok(BTC()),
            "ETH" => Ok(ETH()),
            "USDT" => Ok(USDT()),
            "USDC" => Ok(USDC()),
            _ => Err(anyhow!("Unknown currency: {}", s)),
        }
    }
}

// Common currency constructors
pub fn USD() -> Currency {
    Currency::new("USD", 2, 840, "US Dollar").unwrap()
}

pub fn EUR() -> Currency {
    Currency::new("EUR", 2, 978, "Euro").unwrap()
}

pub fn GBP() -> Currency {
    Currency::new("GBP", 2, 826, "British Pound").unwrap()
}

pub fn JPY() -> Currency {
    Currency::new("JPY", 0, 392, "Japanese Yen").unwrap()
}

pub fn CNY() -> Currency {
    Currency::new("CNY", 2, 156, "Chinese Yuan").unwrap()
}

pub fn BTC() -> Currency {
    Currency::new("BTC", 8, 0, "Bitcoin").unwrap()
}

pub fn ETH() -> Currency {
    Currency::new("ETH", 18, 0, "Ethereum").unwrap()
}

pub fn USDT() -> Currency {
    Currency::new("USDT", 6, 0, "Tether USD").unwrap()
}

pub fn USDC() -> Currency {
    Currency::new("USDC", 6, 0, "USD Coin").unwrap()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_currency_creation() {
        let usd = USD();
        assert_eq!(usd.code_str(), "USD");
        assert_eq!(usd.precision, 2);
        assert_eq!(usd.iso_code, 840);
        assert_eq!(usd.name_str(), "US Dollar");
        assert!(usd.is_fiat());
        assert!(!usd.is_crypto());
    }

    #[test]
    fn test_crypto_currency() {
        let btc = BTC();
        assert_eq!(btc.code_str(), "BTC");
        assert_eq!(btc.precision, 8);
        assert_eq!(btc.iso_code, 0);
        assert!(btc.is_crypto());
        assert!(!btc.is_fiat());
    }

    #[test]
    fn test_currency_from_str() {
        let usd = Currency::from_str("USD").unwrap();
        assert_eq!(usd.code_str(), "USD");

        let cny = Currency::from_str("CNY").unwrap();
        assert_eq!(cny.code_str(), "CNY");

        let btc = Currency::from_str("BTC").unwrap();
        assert_eq!(btc.code_str(), "BTC");

        assert!(Currency::from_str("INVALID").is_err());
    }

    #[test]
    fn test_cny_currency() {
        let cny = CNY();
        assert_eq!(cny.code_str(), "CNY");
        assert_eq!(cny.precision, 2);
        assert_eq!(cny.iso_code, 156);
        assert_eq!(cny.name_str(), "Chinese Yuan");
        assert!(cny.is_fiat());
        assert!(!cny.is_crypto());
    }

    #[test]
    fn test_currency_display() {
        let usd = USD();
        assert_eq!(format!("{}", usd), "USD");

        let cny = CNY();
        assert_eq!(format!("{}", cny), "CNY");
    }
}
