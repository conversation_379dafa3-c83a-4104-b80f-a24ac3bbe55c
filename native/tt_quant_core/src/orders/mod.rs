pub mod types;
pub mod builder;
pub mod validation;

pub use types::*;
pub use builder::OrderBuilder;

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

use crate::types::{Price, Quantity, Money};
use crate::errors::{TtQuantError, TtQuantResult};

/// Order side enumeration
#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OrderSide {
    Buy,
    Sell,
}

impl OrderSide {
    pub fn opposite(&self) -> Self {
        match self {
            Self::Buy => Self::Sell,
            Self::Sell => Self::Buy,
        }
    }
}

/// Order type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OrderType {
    Market,
    Limit,
    Stop,
    StopLimit,
    TrailingStop,
    TrailingStopLimit,
}

/// Order status enumeration
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, <PERSON>q, <PERSON>h, Serialize, Deserialize)]
pub enum OrderStatus {
    Initialized,
    Submitted,
    Accepted,
    Rejected,
    Cancelled,
    Expired,
    Triggered,
    PendingUpdate,
    PendingCancel,
    PartiallyFilled,
    Filled,
}

impl OrderStatus {
    /// Check if order is in a terminal state
    pub fn is_terminal(&self) -> bool {
        matches!(
            self,
            Self::Rejected | Self::Cancelled | Self::Expired | Self::Filled
        )
    }

    /// Check if order is active (can be filled)
    pub fn is_active(&self) -> bool {
        matches!(
            self,
            Self::Accepted | Self::Triggered | Self::PartiallyFilled
        )
    }

    /// Check if order is pending
    pub fn is_pending(&self) -> bool {
        matches!(
            self,
            Self::Initialized | Self::Submitted | Self::PendingUpdate | Self::PendingCancel
        )
    }
}

/// Time in force enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TimeInForce {
    /// Good Till Cancelled
    GTC,
    /// Immediate Or Cancel
    IOC,
    /// Fill Or Kill
    FOK,
    /// Good Till Date
    GTD,
    /// At The Opening
    ATO,
    /// At The Close
    ATC,
}

/// Order identifiers
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct OrderId {
    pub client_order_id: String,
    pub venue_order_id: Option<String>,
}

impl OrderId {
    pub fn new(client_order_id: String) -> Self {
        Self {
            client_order_id,
            venue_order_id: None,
        }
    }

    pub fn with_venue_id(mut self, venue_order_id: String) -> Self {
        self.venue_order_id = Some(venue_order_id);
        self
    }
}

/// Instrument identifier
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct InstrumentId {
    pub symbol: String,
    pub venue: String,
}

impl InstrumentId {
    pub fn new(symbol: String, venue: String) -> Self {
        Self { symbol, venue }
    }

    pub fn to_string(&self) -> String {
        format!("{}.{}", self.symbol, self.venue)
    }
}

/// Core order structure
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Order {
    pub id: OrderId,
    pub instrument_id: InstrumentId,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub stop_price: Option<Price>,
    pub time_in_force: TimeInForce,
    pub status: OrderStatus,
    pub filled_quantity: Quantity,
    pub remaining_quantity: Quantity,
    pub average_fill_price: Option<Price>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub tags: Vec<String>,
}

impl Order {
    /// Create a new order with builder pattern
    pub fn builder() -> OrderBuilder {
        OrderBuilder::new()
    }

    /// Check if order is completely filled
    pub fn is_filled(&self) -> bool {
        self.status == OrderStatus::Filled
    }

    /// Check if order is partially filled
    pub fn is_partially_filled(&self) -> bool {
        self.status == OrderStatus::PartiallyFilled || self.filled_quantity.is_positive()
    }

    /// Get fill percentage (0.0 to 1.0)
    pub fn fill_percentage(&self) -> f64 {
        if self.quantity.is_zero() {
            0.0
        } else {
            self.filled_quantity.as_f64() / self.quantity.as_f64()
        }
    }

    /// Update order status
    pub fn update_status(&mut self, status: OrderStatus) {
        self.status = status;
        self.updated_at = Utc::now();
    }

    /// Add fill to order
    pub fn add_fill(&mut self, fill_quantity: Quantity, fill_price: Price) -> TtQuantResult<()> {
        if fill_quantity.is_zero() {
            return Err(TtQuantError::order_error("Fill quantity cannot be zero"));
        }

        let new_filled = (self.filled_quantity + fill_quantity)
            .map_err(|e| TtQuantError::order_error(e.to_string()))?;

        if new_filled > self.quantity {
            return Err(TtQuantError::order_error("Fill quantity exceeds order quantity"));
        }

        // Update average fill price
        if self.filled_quantity.is_zero() {
            self.average_fill_price = Some(fill_price);
        } else if let Some(avg_price) = self.average_fill_price {
            let total_value = (avg_price * self.filled_quantity.as_f64())
                .map_err(|e| TtQuantError::calculation_error(e.to_string()))?
                .as_f64()
                + (fill_price * fill_quantity.as_f64())
                    .map_err(|e| TtQuantError::calculation_error(e.to_string()))?
                    .as_f64();
            
            let new_avg_price = Price::new(total_value / new_filled.as_f64(), fill_price.precision)
                .map_err(|e| TtQuantError::calculation_error(e.to_string()))?;
            
            self.average_fill_price = Some(new_avg_price);
        }

        self.filled_quantity = new_filled;
        self.remaining_quantity = (self.quantity - self.filled_quantity)
            .map_err(|e| TtQuantError::calculation_error(e.to_string()))?;

        // Update status based on fill
        if self.remaining_quantity.is_zero() {
            self.status = OrderStatus::Filled;
        } else {
            self.status = OrderStatus::PartiallyFilled;
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// Check if order has expired
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            Utc::now() > expires_at
        } else {
            false
        }
    }

    /// Get order value (quantity * price for limit orders)
    pub fn get_order_value(&self) -> Option<Money> {
        // This would need currency information from instrument
        // For now, return None - will be implemented with instrument integration
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::currency::USD;

    #[test]
    fn test_order_side() {
        assert_eq!(OrderSide::Buy.opposite(), OrderSide::Sell);
        assert_eq!(OrderSide::Sell.opposite(), OrderSide::Buy);
    }

    #[test]
    fn test_order_status() {
        assert!(OrderStatus::Filled.is_terminal());
        assert!(OrderStatus::Cancelled.is_terminal());
        assert!(!OrderStatus::Accepted.is_terminal());

        assert!(OrderStatus::Accepted.is_active());
        assert!(!OrderStatus::Filled.is_active());

        assert!(OrderStatus::Submitted.is_pending());
        assert!(!OrderStatus::Filled.is_pending());
    }

    #[test]
    fn test_order_fill() {
        let mut order = Order::builder()
            .client_order_id("test-001".to_string())
            .instrument_id(InstrumentId::new("BTCUSD".to_string(), "binance".to_string()))
            .side(OrderSide::Buy)
            .order_type(OrderType::Limit)
            .quantity(Quantity::new(1.0, 8).unwrap())
            .price(Some(Price::new(50000.0, 2).unwrap()))
            .build()
            .unwrap();

        assert!(!order.is_filled());
        assert!(!order.is_partially_filled());
        assert_eq!(order.fill_percentage(), 0.0);

        // Add partial fill
        let fill_qty = Quantity::new(0.5, 8).unwrap();
        let fill_price = Price::new(50100.0, 2).unwrap();
        order.add_fill(fill_qty, fill_price).unwrap();

        assert!(!order.is_filled());
        assert!(order.is_partially_filled());
        assert_eq!(order.fill_percentage(), 0.5);
        assert_eq!(order.status, OrderStatus::PartiallyFilled);

        // Complete the fill
        let remaining_qty = order.remaining_quantity;
        let final_price = Price::new(50200.0, 2).unwrap();
        order.add_fill(remaining_qty, final_price).unwrap();

        assert!(order.is_filled());
        assert_eq!(order.fill_percentage(), 1.0);
        assert_eq!(order.status, OrderStatus::Filled);
        assert!(order.remaining_quantity.is_zero());
    }
}
