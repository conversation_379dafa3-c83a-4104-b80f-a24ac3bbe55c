use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::types::{Price, Quantity, Money};

/// Order execution report containing fill information
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct OrderFill {
    pub fill_id: String,
    pub order_id: String,
    pub instrument_id: String,
    pub quantity: Quantity,
    pub price: Price,
    pub commission: Option<Money>,
    pub timestamp: DateTime<Utc>,
    pub liquidity_side: LiquiditySide,
}

/// Liquidity side for fills
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum LiquiditySide {
    Maker,
    Taker,
}

/// Order event types for state changes
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum OrderEvent {
    Initialized {
        order_id: String,
        timestamp: DateTime<Utc>,
    },
    Submitted {
        order_id: String,
        timestamp: DateTime<Utc>,
    },
    Accepted {
        order_id: String,
        venue_order_id: String,
        timestamp: DateTime<Utc>,
    },
    Rejected {
        order_id: String,
        reason: String,
        timestamp: DateTime<Utc>,
    },
    Cancelled {
        order_id: String,
        reason: Option<String>,
        timestamp: DateTime<Utc>,
    },
    Expired {
        order_id: String,
        timestamp: DateTime<Utc>,
    },
    Triggered {
        order_id: String,
        timestamp: DateTime<Utc>,
    },
    Filled {
        order_id: String,
        fill: OrderFill,
        timestamp: DateTime<Utc>,
    },
    PartiallyFilled {
        order_id: String,
        fill: OrderFill,
        timestamp: DateTime<Utc>,
    },
    Updated {
        order_id: String,
        changes: OrderUpdateFields,
        timestamp: DateTime<Utc>,
    },
}

/// Fields that can be updated in an order
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct OrderUpdateFields {
    pub quantity: Option<Quantity>,
    pub price: Option<Price>,
    pub stop_price: Option<Price>,
}

/// Order book level for market data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct OrderBookLevel {
    pub price: Price,
    pub quantity: Quantity,
    pub order_count: u32,
}

/// Order book snapshot
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct OrderBookSnapshot {
    pub instrument_id: String,
    pub bids: Vec<OrderBookLevel>,
    pub asks: Vec<OrderBookLevel>,
    pub timestamp: DateTime<Utc>,
    pub sequence: u64,
}

/// Trade tick data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Trade {
    pub trade_id: String,
    pub instrument_id: String,
    pub price: Price,
    pub quantity: Quantity,
    pub side: TradeSide,
    pub timestamp: DateTime<Utc>,
}

/// Trade side (aggressor side)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TradeSide {
    Buy,
    Sell,
}

/// Quote (BBO) data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Quote {
    pub instrument_id: String,
    pub bid_price: Price,
    pub bid_quantity: Quantity,
    pub ask_price: Price,
    pub ask_quantity: Quantity,
    pub timestamp: DateTime<Utc>,
}

/// Bar/Candlestick data
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Bar {
    pub instrument_id: String,
    pub open: Price,
    pub high: Price,
    pub low: Price,
    pub close: Price,
    pub volume: Quantity,
    pub timestamp: DateTime<Utc>,
    pub period: BarPeriod,
}

/// Bar aggregation period
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BarPeriod {
    Second(u32),
    Minute(u32),
    Hour(u32),
    Day(u32),
    Week(u32),
    Month(u32),
}

impl BarPeriod {
    pub fn to_seconds(&self) -> u64 {
        match self {
            Self::Second(n) => *n as u64,
            Self::Minute(n) => *n as u64 * 60,
            Self::Hour(n) => *n as u64 * 3600,
            Self::Day(n) => *n as u64 * 86400,
            Self::Week(n) => *n as u64 * 604800,
            Self::Month(n) => *n as u64 * 2592000, // Approximate
        }
    }
}

/// Position information
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Position {
    pub instrument_id: String,
    pub side: PositionSide,
    pub quantity: Quantity,
    pub average_price: Price,
    pub unrealized_pnl: Money,
    pub realized_pnl: Money,
    pub timestamp: DateTime<Utc>,
}

/// Position side
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PositionSide {
    Long,
    Short,
    Flat,
}

/// Account balance information
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct AccountBalance {
    pub currency: String,
    pub total: Money,
    pub available: Money,
    pub locked: Money,
    pub timestamp: DateTime<Utc>,
}

/// Risk metrics for portfolio
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct RiskMetrics {
    pub total_exposure: Money,
    pub available_margin: Money,
    pub used_margin: Money,
    pub margin_ratio: f64,
    pub unrealized_pnl: Money,
    pub realized_pnl: Money,
    pub timestamp: DateTime<Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::currency::USD;

    #[test]
    fn test_bar_period_conversion() {
        assert_eq!(BarPeriod::Second(30).to_seconds(), 30);
        assert_eq!(BarPeriod::Minute(5).to_seconds(), 300);
        assert_eq!(BarPeriod::Hour(1).to_seconds(), 3600);
        assert_eq!(BarPeriod::Day(1).to_seconds(), 86400);
    }

    #[test]
    fn test_order_fill_creation() {
        let fill = OrderFill {
            fill_id: "fill-001".to_string(),
            order_id: "order-001".to_string(),
            instrument_id: "BTCUSD.binance".to_string(),
            quantity: Quantity::new(0.5, 8).unwrap(),
            price: Price::new(50000.0, 2).unwrap(),
            commission: Some(Money::new(5.0, USD()).unwrap()),
            timestamp: Utc::now(),
            liquidity_side: LiquiditySide::Taker,
        };

        assert_eq!(fill.fill_id, "fill-001");
        assert_eq!(fill.liquidity_side, LiquiditySide::Taker);
    }

    #[test]
    fn test_order_event_serialization() {
        let event = OrderEvent::Accepted {
            order_id: "order-001".to_string(),
            venue_order_id: "venue-123".to_string(),
            timestamp: Utc::now(),
        };

        let serialized = serde_json::to_string(&event).unwrap();
        let deserialized: OrderEvent = serde_json::from_str(&serialized).unwrap();

        match (event, deserialized) {
            (OrderEvent::Accepted { order_id: id1, .. }, OrderEvent::Accepted { order_id: id2, .. }) => {
                assert_eq!(id1, id2);
            }
            _ => panic!("Event type mismatch"),
        }
    }
}
